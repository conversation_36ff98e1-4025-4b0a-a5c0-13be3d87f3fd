# Service Home Moving

React Native micro-frontend for home moving service booking functionality within the bTaskee super app.

## 📋 Table of Contents

- [Getting Started](#getting-started)
- [Development](#development)
- [Testing](#testing)
- [E2E Testing](#e2e-testing)
- [Build & Deployment](#build--deployment)

## 🚀 Getting Started

### Prerequisites

- Node.js >= 18.0.0
- Yarn package manager
- React Native development environment
- iOS: Xcode 15.0+ and iOS Simulator
- Android: Android Studio and Android SDK

### Installation

```bash
# Install dependencies
yarn install

# iOS setup (macOS only)
cd ios && pod install && cd ..

# Start Metro bundler
yarn start

# Run on iOS
yarn ios

# Run on Android
yarn android
```

## 🛠️ Development

### Project Structure

```
src/
├── components/     # Reusable UI components
├── screens/        # Screen components
├── navigation/     # Navigation configuration
├── store/          # State management (Zustand)
├── hooks/          # Custom React hooks
├── config/         # Configuration files
├── constant/       # Constants and enums
├── i18n/           # Internationalization
└── assets/         # Images and static assets
```

### Available Scripts

```bash
# Development
yarn start          # Start Metro bundler
yarn ios            # Run on iOS simulator
yarn android        # Run on Android emulator

# Code Quality
yarn lint           # Run ESLint
yarn lint:fix       # Fix ESLint issues

# Testing
yarn test           # Run unit tests
yarn e2e:build      # Build E2E tests
yarn e2e:test       # Run E2E tests
```

## 🧪 Testing

### Unit Testing

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test --watch

# Run tests with coverage
yarn test --coverage
```

### E2E Testing with Detox

#### Prerequisites

- **Node.js**: >= 18.0.0
- **Detox CLI**: `npm install -g detox-cli`
- **iOS**: Xcode 15.0+, iPhone 16 Pro Max Simulator
- **Android**: Android Studio, Pixel 6 API 34 Emulator
- **applesimutils**: `brew tap wix/brew && brew install applesimutils`

## 🚀 Build & Deployment

### Building for Production

```bash
# Bundle for iOS
yarn bundle:ios

# Bundle for Android
yarn bundle:android

# Build for both platforms
yarn build
```

### Deployment

The service-home-moving module is deployed as part of the bTaskee super app architecture using Module Federation.

## 📚 Documentation

### Project Documentation
- [Business Design Document](./docs/home_moving_BDD.md) - Business requirements and specifications
- [AI Prompt Guide](./docs/### 🤖 Enhanced AI Prompt for Detox E2E .md) - AI-assisted development guidelines

### Architecture
- **Framework**: React Native 0.77.2
- **State Management**: Zustand
- **Navigation**: React Navigation 7.x
- **Internationalization**: i18next
- **Module Federation**: @module-federation/enhanced

## 🤝 Contributing

1. Follow the established coding standards
2. Write tests for new features
3. Update documentation as needed
4. Ensure E2E tests pass before submitting PRs

## 📄 License

This project is part of the bTaskee ecosystem and follows company licensing terms.
