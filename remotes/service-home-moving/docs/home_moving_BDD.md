# Business Design Document - **On‑Demand Home Moving** Service

## **1. Purpose**

Enable users to book a one‑off (On‑Demand) Home Moving service via a mobile app, guiding them through location setup, choose task details, scheduling, and payment in a seamless native experience.

## **2. Scope / Features**

### Choose an Address

* **Property Type Support:** Support for different property categories including houses, apartments, and villas with appropriate service customization for moving requirements
* **Address and Contact Management:** Handle contact details associated with both origin and destination locations including name and phone number for service coordination, allow users to save frequently used addresses and set preferences for future bookings
* **Multi-Location Support:** Support for both pickup and delivery addresses with distance calculation and route optimization

### **Choose Task Details**

* **Moving Size selector** with different volume options based on property size
* **Furniture Type Selection** with categories for electronics, solid furniture, and removable furniture
* **Add-on Service selector** for special items like air conditioners, water heaters, and fragile items
* **Options toggles** for stairs transport, elevator transport, garage pickup, and narrow alley access
* **Task Details** button launching a modal with item-specific checklists and special handling requirements

### Choose Working Time

* **Date and Time Selection:** Flexible scheduling system allowing users to choose preferred moving dates and times with advance booking support
* **Time Slot Management:** Full-day or half-day booking options based on moving requirements and distance
* **Notes for Tasker:** Text-based communication system allowing users to provide specific instructions about fragile items, access requirements, and special handling needs

### Confirm and Payment

* **Booking Confirmation:** Comprehensive booking summary and confirmation system displaying all moving details, pickup/delivery addresses, and item inventory before final commitment
* **Payment Processing:** Multi-channel payment system supporting various payment methods including digital wallets, cards, and alternative payment options
* **Promotion Management:** Discount and promotion code system allowing users to apply available offers and see immediate savings
* **Contact Updates:** Real-time contact information editing capabilities during the booking confirmation process

## 3. Stakeholders

* **End Users:** Homeowners, renters, or individuals booking home moving services who need professional moving assistance for relocating their belongings
* **Movers (Taskers):** Professional moving service providers who will execute the moving tasks according to user specifications, handle furniture and belongings safely
* **Admin:** Platform administrators responsible for service quality oversight, scheduling coordination, pricing management, promotion oversight, and overall system maintenance

## 4. Functional Requirements

### 4.1 Choose an Address

* **Conditional Navigation:** The system checks user's saved addresses upon entering the booking flow:
  * If user has at least one saved address: System automatically proceeds to "Choose Task Details" page, skipping address selection. User can modify address selection at this page if needed.
  * If user has no saved addresses: System directs user to "Choose Location" page for address setup
* **Multi-Input Address System:** Users can input both pickup and delivery addresses through three distinct methods:
  * GPS location detection for automatic current location identification and nearby address suggestion
  * Manual text entry with auto-complete and address suggestion functionality for precise address specification
  * Map-based selection with drag-and-drop pin placement for visual address confirmation

### 4.2 Choose Task Details

* **Moving Volume Selection:** Users select appropriate moving size based on property type and furniture volume
* **Furniture Categorization:** System provides categorized selection for different types of items requiring special handling
* **Add-on Services:** Optional services for specialized items like appliances, electronics, and fragile belongings
* **Access Options:** Selection of pickup and delivery access methods (stairs, elevator, garage, narrow alley)
* **Special Requirements:** Input field for specific moving requirements and fragile item handling instructions

### 4.3 Choose Working Time

* **Date Selection:** Calendar interface with availability checking and advance booking support (up to 15 days)
* **Time Slot Selection:** Available time slots based on distance, volume, and tasker availability
* **Duration Estimation:** Automatic duration calculation based on selected items, distance, and access requirements
* **Special Instructions:** Text area for detailed moving instructions and special handling requirements

### 4.4 Confirm and Payment

* **Booking Summary:** Complete overview of moving details including pickup/delivery addresses, items, time, and pricing
* **Payment Method Selection:** Multiple payment options with secure processing
* **Promotion Application:** Discount code input with real-time price calculation
* **Final Confirmation:** Order placement with booking confirmation and tasker assignment

## 5. Technical Requirements

### 5.1 Platform Compatibility
* iOS and Android mobile applications
* Responsive design for various screen sizes
* Offline capability for basic functionality

### 5.2 Performance Requirements
* App launch time: < 3 seconds
* Page transition time: < 1 second
* Payment processing: < 10 seconds
* Real-time updates for tasker location and status

### 5.3 Security Requirements
* Secure payment processing with PCI compliance
* User data encryption and privacy protection
* Secure communication between app and backend services

## 6. User Experience Requirements

### 6.1 Accessibility
* Support for screen readers and accessibility features
* High contrast mode support
* Large text options for visually impaired users

### 6.2 Localization
* Multi-language support (Vietnamese, English, Thai, Indonesian, Malay)
* Currency and date format localization
* Cultural adaptation for different markets

### 6.3 Error Handling
* Clear error messages with actionable solutions
* Graceful degradation for network issues
* Retry mechanisms for failed operations

## 7. Business Rules

### 7.1 Booking Constraints
* Advance booking: 2 hours to 15 days in advance
* Service area limitations based on city coverage
* Maximum distance limitations for single booking
* Minimum booking value requirements

### 7.2 Pricing Rules
* Distance-based pricing calculation
* Volume and weight-based pricing tiers
* Add-on service pricing structure
* Dynamic pricing based on demand and availability

### 7.3 Cancellation Policy
* Free cancellation up to 2 hours before scheduled time
* Partial refund for cancellations within 2 hours
* No refund for no-show or same-day cancellations

## 8. Success Metrics

### 8.1 User Engagement
* Booking completion rate > 85%
* User retention rate > 70%
* Average session duration > 5 minutes

### 8.2 Service Quality
* Customer satisfaction score > 4.5/5
* On-time completion rate > 95%
* Damage/complaint rate < 2%

### 8.3 Business Performance
* Revenue growth month-over-month
* Market share expansion in target cities
* Operational efficiency improvements
