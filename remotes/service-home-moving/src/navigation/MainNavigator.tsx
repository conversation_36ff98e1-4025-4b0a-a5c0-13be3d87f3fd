import '../i18n';

import React, { useCallback, useEffect, useMemo } from 'react';
import {
  BlockView,
  ColorsV2,
  ConfigHelpers,
  CText,
  getDefaultPaymentMethod,
  IconAssets,
  IconImage,
  IService,
  NavBar,
  SERVICES,
  Spacing,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackHeaderProps,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';
import { usePostTaskStore } from '@store';

import { useI18n } from '@hooks';
import {
  ChooseAddress,
  ChooseDateTime,
  ChooseDuration,
  ConfirmBooking,
  PostTaskSuccess,
} from '@screens';

import { RouteName } from './RouteName';
import { RootStackParamList } from './types';

const Stack = createNativeStackNavigator<RootStackParamList>();

// Extract component outside of render to prevent reconciliation issues
interface AddressTitleProps {
  shortAddress?: string;
  address?: string;
}

const AddressTitle: React.FC<AddressTitleProps> = React.memo(
  ({ shortAddress, address }) => {
    if (!shortAddress && !address) {
      return null;
    }

    return (
      <BlockView
        flex
        row
        horizontal
      >
        <IconImage
          source={IconAssets.icLocation}
          size={24}
          color={ColorsV2.red500}
        />
        <BlockView margin={{ left: Spacing.SPACE_08 }}>
          <CText color={ColorsV2.neutral400}>{shortAddress}</CText>
          <CText
            fontFamily="bold"
            numberOfLines={1}
            margin={{ right: Spacing.SPACE_16 }}
            color={ColorsV2.neutral800}
          >
            {address}
          </CText>
        </BlockView>
      </BlockView>
    );
  },
);

AddressTitle.displayName = 'AddressTitle';

const MainNavigator = () => {
  const { t } = useI18n();
  const { address, setService, setPaymentMethod } = usePostTaskStore();
  const settings = useSettingsStore().settings;

  // Memoize the home moving service to avoid unnecessary re-computations
  const homeMovingService = useMemo(() => {
    return settings?.services?.find(
      (service: IService) => service?.name === SERVICES.HOME_MOVING,
    );
  }, [settings?.services]);

  // Use useCallback to prevent unnecessary re-renders
  const initData = useCallback(async () => {
    if (homeMovingService) {
      setService(homeMovingService);
    }
    setPaymentMethod(getDefaultPaymentMethod());
  }, [homeMovingService, setService, setPaymentMethod]);

  useEffect(() => {
    initData();
  }, [initData]);

  // Memoize the address title component to avoid recreation
  const addressTitleComponent = useMemo(
    () => (
      <AddressTitle
        shortAddress={address?.shortAddress}
        address={address?.address}
      />
    ),
    [address?.shortAddress, address?.address],
  );

  // Screen options following the established pattern across all microservices
  const screenOptions = useCallback(
    ({ navigation }: any): NativeStackNavigationOptions => ({
      headerShown: true,
      animation: 'slide_from_right',
      animationDuration: 200,
      contentStyle: { backgroundColor: ColorsV2.neutralWhite },
      // eslint-disable-next-line react/no-unstable-nested-components
      header: (props: NativeStackHeaderProps) => {
        // Get the title from options
        const getTitle = () => {
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'function'
          ) {
            // @ts-ignore - React Navigation headerTitle function compatibility
            return props.options.headerTitle();
          }
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'string'
          ) {
            return props.options.headerTitle;
          }
          if (props?.options?.title) {
            return props.options.title;
          }
          return '';
        };

        return (
          <NavBar
            // @ts-ignore - NavBar title accepts ReactNode but types are strict
            title={getTitle()}
            backgroundColor={ColorsV2.neutralWhite}
            onGoBack={() => navigation.goBack()}
            isShadow={true}
          />
        );
      },
    }),
    [],
  );

  return (
    <Stack.Navigator
      screenOptions={screenOptions}
      initialRouteName={RouteName.ChooseAddress}
    >
      <Stack.Screen
        name={RouteName.ChooseAddress}
        component={ChooseAddress}
        options={{
          title: t('LIST_OF_LOCATIONS'),
        }}
      />
      <Stack.Screen
        name={RouteName.ChooseDuration}
        component={ChooseDuration}
        options={{
          headerTitle: () => addressTitleComponent,
        }}
      />
      <Stack.Screen
        name={RouteName.ChooseDateTime}
        component={ChooseDateTime}
        options={{
          title: t('WORK_TIME_TITLE'),
        }}
      />
      <Stack.Screen
        name={RouteName.ConfirmAndPayment}
        component={ConfirmBooking}
        options={{
          title: t('PT2_CONFIRM_HEADER_TITLE'),
        }}
      />
      <Stack.Screen
        name={RouteName.PostTaskSuccess}
        component={PostTaskSuccess}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
